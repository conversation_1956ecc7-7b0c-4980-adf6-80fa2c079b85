import math
import json
import numpy as np
import pandas as pd
import yfinance as yf

# Mapping of A-share codes to Yahoo Finance tickers
TICKERS = {
    '601869.SH': '601869.SS',  # 长飞光纤
    '600522.SH': '600522.SS',  # 中天科技
    '600487.SH': '600487.SS',  # 亨通光电
    '000300.SH': '000300.SS',  # 沪深300
}

PERIOD = '9mo'   # fetch enough history to compute 3m/60d stats reliably
INTERVAL = '1d'
RF = 0.02  # annual risk-free rate for Sharpe
WIN_3M = 63  # ~3 months trading days


def _download():
    data = yf.download(list(TICKERS.values()), period=PERIOD, interval=INTERVAL, auto_adjust=False, progress=False)
    def to_df(symbol):
        df = pd.DataFrame({
            'open': data['Open'][symbol],
            'high': data['High'][symbol],
            'low': data['Low'][symbol],
            'close': data['Close'][symbol],
            'volume': data['Volume'][symbol],
        }).dropna()
        return df
    frames = {k: to_df(v) for k, v in TICKERS.items()}
    for df in frames.values():
        df['ret'] = df['close'].pct_change()
    return frames


def rsi14(close: pd.Series) -> pd.Series:
    delta = close.diff()
    gain = delta.clip(lower=0).ewm(alpha=1/14, adjust=False).mean()
    loss = (-delta.clip(upper=0)).ewm(alpha=1/14, adjust=False).mean()
    rs = gain / loss.replace(0, np.nan)
    return 100 - 100 / (1 + rs)


def macd(close: pd.Series):
    ema12 = close.ewm(span=12, adjust=False).mean()
    ema26 = close.ewm(span=26, adjust=False).mean()
    macd_val = ema12 - ema26
    signal = macd_val.ewm(span=9, adjust=False).mean()
    hist = macd_val - signal
    return macd_val, signal, hist


def atr14(df: pd.DataFrame) -> float:
    prev_close = df['close'].shift(1)
    tr = pd.concat([
        (df['high'] - df['low']).abs(),
        (df['high'] - prev_close).abs(),
        (df['low'] - prev_close).abs()
    ], axis=1).max(axis=1)
    return float(tr.rolling(14).mean().iloc[-1])


def support_resistance(close: pd.Series) -> dict:
    # Use quantiles and recent swing high/low as simple proxies
    recent = close.tail(WIN_3M)
    q10, q50, q90 = recent.quantile([0.10, 0.50, 0.90])
    swing_high = recent.rolling(5, center=True).max().iloc[-10:].max()
    swing_low = recent.rolling(5, center=True).min().iloc[-10:].min()
    return {
        'q10': float(q10),
        'median': float(q50),
        'q90': float(q90),
        'swing_high': float(swing_high),
        'swing_low': float(swing_low),
    }


def compute_metrics(frames: dict) -> dict:
    idx = frames['000300.SH'].tail(WIN_3M).copy()
    out = {}
    for sym in ['601869.SH', '600522.SH', '600487.SH']:
        df_full = frames[sym].copy()
        df = df_full.tail(WIN_3M).copy()

        mu = df['ret'].mean()
        sigma = df['ret'].std()
        ann_sigma = float(sigma * math.sqrt(252)) if not np.isnan(sigma) else None
        cumret = float((1 + df['ret'].dropna()).prod() - 1)
        mdd = float(((df['close']/df['close'].cummax())-1).min())
        sharpe = float(((mu * 252 - RF) / (sigma * math.sqrt(252)))) if sigma and sigma > 0 else None

        vol_mean = float(df['volume'].mean()) if df['volume'].size else None
        vol_last5 = float(df['volume'].tail(5).mean()) if df['volume'].size else None
        vol_chg = (vol_last5/vol_mean - 1) if vol_mean and vol_mean > 0 else None

        ema20 = df['close'].ewm(span=20, adjust=False).mean()
        ema50 = df['close'].ewm(span=50, adjust=False).mean()
        slope_ema20 = float((ema20.iloc[-1] - ema20.iloc[-5]) / 5)
        slope_ema50 = float((ema50.iloc[-1] - ema50.iloc[-5]) / 5)
        ma_state = 'golden' if ema20.iloc[-1] > ema50.iloc[-1] else 'death'

        rsi_last = float(rsi14(df['close']).iloc[-1])
        macd_val, signal, hist = macd(df['close'])
        macd_last = float(macd_val.iloc[-1])
        hist_last = float(hist.iloc[-1])

        mb = df['close'].rolling(20).mean()
        sd = df['close'].rolling(20).std()
        upper = mb + 2*sd
        lower = mb - 2*sd
        bb_pos = float((df['close'].iloc[-1] - lower.iloc[-1]) / (upper.iloc[-1] - lower.iloc[-1])) if not np.isnan(upper.iloc[-1]) else None

        atr_val = atr14(df)
        supres = support_resistance(df['close'])

        # relative to index
        ri = df[['ret']].join(idx['ret'], how='inner', rsuffix='_idx').dropna()
        if len(ri) > 10 and np.var(ri['ret_idx']) > 0:
            X = ri['ret_idx'].values
            Y = ri['ret'].values
            beta = float(np.cov(X, Y)[0, 1] / np.var(X))
            alpha = float(Y.mean() * 252 - beta * X.mean() * 252)
            corr = float(np.corrcoef(X, Y)[0, 1])
            ex_ret = float((1 + Y).prod() / (1 + X).prod() - 1)
        else:
            beta = alpha = corr = ex_ret = None

        # forecast next 20 trading days using last 60d mu/sigma
        base = df_full.copy()
        base['ret'] = base['close'].pct_change()
        recent = base.tail(60)
        mu60 = recent['ret'].mean()
        s60 = recent['ret'].std()
        exp20 = float(mu60 * 20) if not np.isnan(mu60) else None
        s20 = float(s60 * math.sqrt(20)) if not np.isnan(s60) else None
        rng68 = (exp20 - s20, exp20 + s20) if (exp20 is not None and s20 is not None) else None
        rng95 = (exp20 - 2*s20, exp20 + 2*s20) if (exp20 is not None and s20 is not None) else None

        out[sym] = {
            'last_close': float(df['close'].iloc[-1]),
            'cumret_3m': cumret,
            'mu_d': float(mu) if not np.isnan(mu) else None,
            'sigma_d': float(sigma) if not np.isnan(sigma) else None,
            'ann_sigma': ann_sigma,
            'mdd': mdd,
            'sharpe': sharpe,
            'vol_mean': vol_mean,
            'vol_last5': vol_last5,
            'vol_chg5_vs_mean': float(vol_chg) if vol_chg is not None else None,
            'ema20_slope_per_day': slope_ema20,
            'ema50_slope_per_day': slope_ema50,
            'ma_state': ma_state,
            'rsi14': rsi_last,
            'macd': macd_last,
            'macd_hist': hist_last,
            'bb_pos_0to1': bb_pos,
            'atr14': float(atr_val) if not np.isnan(atr_val) else None,
            'support_resistance': supres,
            'beta': beta,
            'alpha_ann': alpha,
            'corr_idx': corr,
            'excess_ret_vs_idx': ex_ret,
            'pred_exp20': exp20,
            'pred_68': rng68,
            'pred_95': rng95,
        }
    return out


def main():
    frames = _download()
    result = compute_metrics(frames)
    print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == '__main__':
    main()

